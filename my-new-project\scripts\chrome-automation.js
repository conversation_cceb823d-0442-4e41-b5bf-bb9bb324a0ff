const { chromium } = require('playwright');
const path = require('path');
const os = require('os');

async function launchChromeWithUserData() {
  // Chrome用户数据目录路径（Windows）
  const userDataDir = 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data';
  
  // 启动Chrome浏览器，使用现有的用户数据
  const browser = await chromium.launchPersistentContext(userDataDir, {
    headless: false,
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', // Chrome路径
    viewport: { width: 1280, height: 720 },
    args: [
      '--start-maximized',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });

  const page = browser.pages()[0] || await browser.newPage();
  
  try {
    console.log('使用您的Chrome浏览器访问百度...');
    await page.goto('https://www.baidu.com');
    
    console.log('搜索三峡大学...');
    await page.fill('#kw', '三峡大学');
    await page.click('#su');
    
    // 等待搜索结果
    await page.waitForSelector('.result', { timeout: 10000 });
    
    console.log('搜索完成！您可以在浏览器中查看结果');
    console.log('由于使用了您的Chrome用户数据，所有保存的密码和登录状态都可用');
    
    // 保持浏览器打开，让用户查看
    console.log('浏览器将保持打开状态，按Ctrl+C退出脚本');
    
    // 等待用户操作
    await new Promise(() => {}); // 无限等待
    
  } catch (error) {
    console.error('操作失败:', error);
  }
}

// 运行脚本
launchChromeWithUserData().catch(console.error);
