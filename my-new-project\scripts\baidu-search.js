const puppeteer = require('puppeteer');

async function searchBaidu() {
  // 启动浏览器
  const browser = await puppeteer.launch({
    headless: false, // 设置为false以显示浏览器窗口
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', // 使用系统Chrome
    defaultViewport: null,
    args: ['--start-maximized']
  });

  const page = await browser.newPage();

  try {
    console.log('正在访问百度...');
    // 访问百度
    await page.goto('https://www.baidu.com', { waitUntil: 'networkidle2' });

    console.log('正在搜索三峡大学...');
    // 找到搜索框并输入"三峡大学"
    await page.type('#kw', '三峡大学');

    // 点击搜索按钮
    await page.click('#su');

    // 等待搜索结果加载
    await page.waitForSelector('.result', { timeout: 10000 });

    console.log('搜索完成！正在获取结果...');

    // 获取搜索结果
    const results = await page.evaluate(() => {
      const resultElements = document.querySelectorAll('.result h3 a');
      return Array.from(resultElements).slice(0, 5).map(link => ({
        title: link.textContent.trim(),
        url: link.href
      }));
    });

    console.log('搜索结果：');
    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.title}`);
      console.log(`   ${result.url}\n`);
    });

    // 等待5秒让您查看结果
    await page.waitForTimeout(5000);

  } catch (error) {
    console.error('搜索过程中出现错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行搜索
searchBaidu().catch(console.error);
